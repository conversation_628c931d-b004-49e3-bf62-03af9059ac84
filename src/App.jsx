import React from 'react';
import { ThemeProvider } from './components/ThemeProvider';
import SmoothScroll from './components/SmoothScroll';
import Navigation from './components/Navigation';
import ScrollIndicator from './components/ScrollIndicator';
import HeroSection from './components/sections/HeroSection';
import AboutSection from './components/sections/AboutSection';
import ExperienceSection from './components/sections/ExperienceSection';
import ProjectsSection from './components/sections/ProjectsSection';
import EducationSection from './components/sections/EducationSection';
import ContactSection from './components/sections/ContactSection';

function App() {
  return (
    <ThemeProvider>
      <SmoothScroll>
        <div className="min-h-screen bg-white dark:bg-dark-900 text-gray-900 dark:text-gray-100">
          <ScrollIndicator />
          <Navigation />

          {/* Hero Section */}
          <HeroSection />

          {/* About Section */}
          <AboutSection />

          {/* Experience Section */}
          <ExperienceSection />

          {/* Projects Section */}
          <ProjectsSection />

          {/* Education Section */}
          <EducationSection />

          {/* Contact Section */}
          <ContactSection />
        </div>
      </SmoothScroll>
    </ThemeProvider>
  );
}

export default App;
