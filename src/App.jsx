import React, { useState } from 'react';
import { ThemeProvider } from './components/ThemeProvider';
import SmoothScroll from './components/SmoothScroll';
import Navigation from './components/Navigation';
import ScrollIndicator from './components/ScrollIndicator';
import SectionIndicator from './components/SectionIndicator';
import LoadingScreen from './components/LoadingScreen';
import CustomCursor from './components/CustomCursor';
import PerformanceMonitor from './components/PerformanceMonitor';
import { FloatingParticles, EasterEgg } from './components/MicroInteractions';
import Footer from './components/Footer';
import HeroSection from './components/sections/HeroSection';
import AboutSection from './components/sections/AboutSection';
import ExperienceSection from './components/sections/ExperienceSection';
import ProjectsSection from './components/sections/ProjectsSection';
import EducationSection from './components/sections/EducationSection';
import ContactSection from './components/sections/ContactSection';

function App() {
  const [isLoading, setIsLoading] = useState(true);

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  return (
    <ThemeProvider>
      {isLoading ? (
        <LoadingScreen onLoadingComplete={handleLoadingComplete} />
      ) : (
        <SmoothScroll>
          <div className="min-h-screen bg-white dark:bg-dark-900 text-gray-900 dark:text-gray-100">
            <FloatingParticles count={15} />
            <CustomCursor />
            <ScrollIndicator />
            <Navigation />
            <SectionIndicator />
            <PerformanceMonitor />
            <EasterEgg />

            {/* Hero Section */}
            <HeroSection />

            {/* About Section */}
            <AboutSection />

            {/* Experience Section */}
            <ExperienceSection />

            {/* Projects Section */}
            <ProjectsSection />

            {/* Education Section */}
            <EducationSection />

            {/* Contact Section */}
            <ContactSection />

            {/* Footer */}
            <Footer />
          </div>
        </SmoothScroll>
      )}
    </ThemeProvider>
  );
}

export default App;
