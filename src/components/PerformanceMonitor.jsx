import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const PerformanceMonitor = ({ showInProduction = false }) => {
  const [metrics, setMetrics] = useState({
    fps: 0,
    memory: 0,
    loadTime: 0,
    isVisible: false
  });

  const [showMetrics, setShowMetrics] = useState(false);

  useEffect(() => {
    // Only show in development or if explicitly enabled
    if (process.env.NODE_ENV === 'production' && !showInProduction) {
      return;
    }

    let frameCount = 0;
    let lastTime = performance.now();
    let animationId;

    // FPS Counter
    const updateFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        setMetrics(prev => ({ ...prev, fps }));
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(updateFPS);
    };

    // Memory Usage (if available)
    const updateMemory = () => {
      if (performance.memory) {
        const memory = Math.round(performance.memory.usedJSHeapSize / 1048576); // MB
        setMetrics(prev => ({ ...prev, memory }));
      }
    };

    // Load Time
    const loadTime = Math.round(performance.now());
    setMetrics(prev => ({ ...prev, loadTime }));

    // Start monitoring
    updateFPS();
    const memoryInterval = setInterval(updateMemory, 1000);

    // Keyboard shortcut to toggle metrics (Ctrl/Cmd + Shift + P)
    const handleKeyPress = (e) => {
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
        setShowMetrics(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);

    return () => {
      cancelAnimationFrame(animationId);
      clearInterval(memoryInterval);
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [showInProduction]);

  // Performance warnings
  const getPerformanceStatus = () => {
    if (metrics.fps < 30) return { status: 'poor', color: 'text-red-500' };
    if (metrics.fps < 50) return { status: 'fair', color: 'text-yellow-500' };
    return { status: 'good', color: 'text-green-500' };
  };

  const performanceStatus = getPerformanceStatus();

  if (process.env.NODE_ENV === 'production' && !showInProduction) {
    return null;
  }

  return (
    <>
      {/* Toggle Button */}
      <motion.button
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 2 }}
        onClick={() => setShowMetrics(!showMetrics)}
        className="fixed bottom-4 left-4 z-50 w-12 h-12 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-200"
        title="Toggle Performance Metrics (Ctrl+Shift+P)"
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      </motion.button>

      {/* Metrics Panel */}
      <AnimatePresence>
        {showMetrics && (
          <motion.div
            initial={{ opacity: 0, x: -300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -300 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="fixed top-20 left-4 z-40 bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 p-4 min-w-[250px]"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Performance
              </h3>
              <button
                onClick={() => setShowMetrics(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
            </div>

            <div className="space-y-3">
              {/* FPS */}
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">FPS:</span>
                <span className={`text-sm font-mono ${performanceStatus.color}`}>
                  {metrics.fps}
                </span>
              </div>

              {/* Memory */}
              {metrics.memory > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Memory:</span>
                  <span className="text-sm font-mono text-gray-900 dark:text-white">
                    {metrics.memory} MB
                  </span>
                </div>
              )}

              {/* Load Time */}
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">Load Time:</span>
                <span className="text-sm font-mono text-gray-900 dark:text-white">
                  {metrics.loadTime} ms
                </span>
              </div>

              {/* Performance Status */}
              <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                  <span className={`text-sm font-medium capitalize ${performanceStatus.color}`}>
                    {performanceStatus.status}
                  </span>
                </div>
              </div>

              {/* Performance Tips */}
              {performanceStatus.status !== 'good' && (
                <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {performanceStatus.status === 'poor' 
                      ? 'Consider reducing animations or closing other tabs'
                      : 'Performance could be improved'
                    }
                  </p>
                </div>
              )}
            </div>

            <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
              <p className="text-xs text-gray-400 dark:text-gray-500">
                Press Ctrl+Shift+P to toggle
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default PerformanceMonitor;
