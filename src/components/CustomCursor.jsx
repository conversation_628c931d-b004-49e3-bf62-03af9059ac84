import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const CustomCursor = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const [isClicking, setIsClicking] = useState(false);

  useEffect(() => {
    const updateMousePosition = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    const handleMouseDown = () => setIsClicking(true);
    const handleMouseUp = () => setIsClicking(false);

    const handleMouseEnter = (e) => {
      if (e.target.matches('button, a, [role="button"], input, textarea, select')) {
        setIsHovering(true);
      }
    };

    const handleMouseLeave = (e) => {
      if (e.target.matches('button, a, [role="button"], input, textarea, select')) {
        setIsHovering(false);
      }
    };

    window.addEventListener('mousemove', updateMousePosition);
    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mouseenter', handleMouseEnter, true);
    document.addEventListener('mouseleave', handleMouseLeave, true);

    return () => {
      window.removeEventListener('mousemove', updateMousePosition);
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mouseenter', handleMouseEnter, true);
      document.removeEventListener('mouseleave', handleMouseLeave, true);
    };
  }, []);

  const cursorVariants = {
    default: {
      x: mousePosition.x - 16,
      y: mousePosition.y - 16,
      scale: 1,
    },
    hover: {
      x: mousePosition.x - 24,
      y: mousePosition.y - 24,
      scale: 1.5,
    },
    click: {
      x: mousePosition.x - 12,
      y: mousePosition.y - 12,
      scale: 0.8,
    }
  };

  const dotVariants = {
    default: {
      x: mousePosition.x - 2,
      y: mousePosition.y - 2,
    },
    hover: {
      x: mousePosition.x - 2,
      y: mousePosition.y - 2,
    },
    click: {
      x: mousePosition.x - 2,
      y: mousePosition.y - 2,
    }
  };

  const getVariant = () => {
    if (isClicking) return 'click';
    if (isHovering) return 'hover';
    return 'default';
  };

  return (
    <div className="hidden lg:block pointer-events-none fixed inset-0 z-50">
      {/* Outer cursor */}
      <motion.div
        variants={cursorVariants}
        animate={getVariant()}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 28,
          mass: 0.5
        }}
        className="absolute w-8 h-8 border-2 border-primary-600 rounded-full mix-blend-difference"
      />
      
      {/* Inner dot */}
      <motion.div
        variants={dotVariants}
        animate={getVariant()}
        transition={{
          type: "spring",
          stiffness: 1000,
          damping: 35,
          mass: 0.1
        }}
        className="absolute w-1 h-1 bg-primary-600 rounded-full"
      />
    </div>
  );
};

export default CustomCursor;
