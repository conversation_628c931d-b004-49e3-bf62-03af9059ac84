import React from 'react';
import { motion } from 'framer-motion';

const ResponsiveContainer = ({ 
  children, 
  className = '', 
  maxWidth = '7xl',
  padding = 'px-4 sm:px-6 lg:px-8',
  ...props 
}) => {
  const maxWidthClasses = {
    'sm': 'max-w-sm',
    'md': 'max-w-md',
    'lg': 'max-w-lg',
    'xl': 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
    'full': 'max-w-full'
  };

  return (
    <div className={`${maxWidthClasses[maxWidth]} mx-auto ${padding} ${className}`} {...props}>
      {children}
    </div>
  );
};

export const ResponsiveGrid = ({ 
  children, 
  cols = { sm: 1, md: 2, lg: 3 },
  gap = 'gap-6',
  className = '',
  ...props 
}) => {
  const gridCols = `grid-cols-${cols.sm} md:grid-cols-${cols.md} lg:grid-cols-${cols.lg}`;
  
  return (
    <div className={`grid ${gridCols} ${gap} ${className}`} {...props}>
      {children}
    </div>
  );
};

export const ResponsiveText = ({ 
  children, 
  size = { sm: 'text-base', md: 'text-lg', lg: 'text-xl' },
  className = '',
  ...props 
}) => {
  const textSize = `${size.sm} md:${size.md} lg:${size.lg}`;
  
  return (
    <div className={`${textSize} ${className}`} {...props}>
      {children}
    </div>
  );
};

export const ResponsiveSpacing = ({ 
  children, 
  py = { sm: 'py-8', md: 'py-12', lg: 'py-16' },
  px = { sm: 'px-4', md: 'px-6', lg: 'px-8' },
  className = '',
  ...props 
}) => {
  const spacing = `${py.sm} md:${py.md} lg:${py.lg} ${px.sm} md:${px.md} lg:${px.lg}`;
  
  return (
    <div className={`${spacing} ${className}`} {...props}>
      {children}
    </div>
  );
};

export const MobileOptimized = ({ children, showOnMobile = true, showOnDesktop = true }) => {
  let classes = '';
  
  if (showOnMobile && !showOnDesktop) {
    classes = 'block lg:hidden';
  } else if (!showOnMobile && showOnDesktop) {
    classes = 'hidden lg:block';
  } else if (!showOnMobile && !showOnDesktop) {
    classes = 'hidden';
  }
  
  return <div className={classes}>{children}</div>;
};

export const PerformanceOptimized = ({ 
  children, 
  reduceMotion = true,
  lazyLoad = true,
  ...props 
}) => {
  const motionProps = reduceMotion ? {
    initial: false,
    animate: false,
    transition: { duration: 0 }
  } : props;

  return (
    <motion.div
      {...motionProps}
      className="will-change-transform"
    >
      {children}
    </motion.div>
  );
};

export default ResponsiveContainer;
