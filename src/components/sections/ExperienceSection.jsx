import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  Building2, 
  Calendar, 
  MapPin, 
  ExternalLink,
  Heart,
  GraduationCap,
  CreditCard,
  Globe
} from 'lucide-react';

const ExperienceSection = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const experiences = [
    {
      id: 1,
      title: "Software Developer",
      company: "Elizabeth Glazer Pediatric Foundation (EGPAF)",
      location: "Lilongwe, Malawi",
      period: "Jan 2023 - Present",
      type: "Full-time",
      icon: Heart,
      color: "from-red-500 to-pink-500",
      description: "Develop the Electronic medical record system (EMR) used across the country for treatment and care. Led the first international deployment of the EMR in Democratic Republic of Congo.",
      achievements: [
        "Led international EMR deployment to DRC",
        "Developed healthcare systems serving thousands of patients",
        "Implemented critical HIV treatment and care modules",
        "Collaborated with international healthcare teams"
      ],
      technologies: ["Java", "PHP", "MySQL", "JavaScript", "Healthcare Systems"]
    },
    {
      id: 2,
      title: "Software Developer",
      company: "Technix",
      location: "Malawi",
      period: "Mar 2022 - Dec 2022",
      type: "Full-time",
      icon: GraduationCap,
      color: "from-blue-500 to-cyan-500",
      description: "Developed and maintained the school inspection system used by the Ministry of Education.",
      achievements: [
        "Built comprehensive school inspection platform",
        "Integrated with Ministry of Education systems",
        "Improved education quality monitoring processes",
        "Delivered user-friendly administrative interfaces"
      ],
      technologies: ["PHP", "JavaScript", "MySQL", "Web Development", "Government Systems"]
    },
    {
      id: 3,
      title: "Software Consultancy",
      company: "Aurora Borealis",
      location: "South Africa (Remote)",
      period: "Sept 2021 - March 2022",
      type: "Contract",
      icon: Globe,
      color: "from-green-500 to-emerald-500",
      description: "Provided remote software development consultancy services for international projects.",
      achievements: [
        "Delivered remote consulting solutions",
        "Worked with international development teams",
        "Implemented scalable software architectures",
        "Maintained high-quality code standards"
      ],
      technologies: ["Remote Work", "Consulting", "Full-stack Development", "International Projects"]
    },
    {
      id: 4,
      title: "Software Engineer Lead",
      company: "Baobab Health Trust",
      location: "Malawi",
      period: "Feb 2020 - Dec 2021",
      type: "Full-time",
      icon: Heart,
      color: "from-purple-500 to-violet-500",
      description: "Led software engineering initiatives for healthcare technology solutions.",
      achievements: [
        "Led development team for healthcare solutions",
        "Architected scalable health information systems",
        "Mentored junior developers",
        "Implemented best practices and code standards"
      ],
      technologies: ["Leadership", "Healthcare IT", "Team Management", "System Architecture"]
    },
    {
      id: 5,
      title: "Software Engineer & Database Officer",
      company: "Credit Data CRB",
      location: "Malawi",
      period: "July 2018 - Feb 2020",
      type: "Full-time",
      icon: CreditCard,
      color: "from-orange-500 to-red-500",
      description: "Developed in-house systems to produce credit reports for banks in Malawi.",
      achievements: [
        "Built credit reporting systems for financial institutions",
        "Managed database operations and optimization",
        "Developed secure financial data processing systems",
        "Ensured compliance with financial regulations"
      ],
      technologies: ["Java", "Database Management", "Financial Systems", "Data Security"]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="experience" className="py-20 px-4 bg-white dark:bg-dark-900" ref={ref}>
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent">
            Professional Experience
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-600 to-primary-400 mx-auto mb-8"></div>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            A journey through impactful projects across healthcare, education, and financial sectors
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="relative"
        >
          {/* Timeline Line */}
          <div className="absolute left-8 md:left-1/2 transform md:-translate-x-px top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-600 to-primary-400"></div>

          {experiences.map((exp, index) => (
            <motion.div
              key={exp.id}
              variants={itemVariants}
              className={`relative flex items-center mb-12 ${
                index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
              }`}
            >
              {/* Timeline Node */}
              <div className="absolute left-8 md:left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white dark:bg-dark-900 border-4 border-primary-600 rounded-full z-10"></div>

              {/* Content Card */}
              <motion.div
                whileHover={{ scale: 1.02, y: -5 }}
                className={`w-full md:w-5/12 ${
                  index % 2 === 0 ? 'md:mr-auto md:pr-8' : 'md:ml-auto md:pl-8'
                } ml-16 md:ml-0`}
              >
                <div className="bg-white dark:bg-dark-700 p-6 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-600">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className={`p-3 rounded-lg bg-gradient-to-r ${exp.color}`}>
                        <exp.icon className="text-white" size={24} />
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                          {exp.title}
                        </h3>
                        <p className="text-primary-600 dark:text-primary-400 font-medium">
                          {exp.company}
                        </p>
                      </div>
                    </div>
                    <span className="bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 px-3 py-1 rounded-full text-sm font-medium">
                      {exp.type}
                    </span>
                  </div>

                  {/* Meta Info */}
                  <div className="flex flex-wrap gap-4 mb-4 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Calendar size={16} />
                      <span>{exp.period}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MapPin size={16} />
                      <span>{exp.location}</span>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">
                    {exp.description}
                  </p>

                  {/* Achievements */}
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Key Achievements:</h4>
                    <ul className="space-y-1">
                      {exp.achievements.map((achievement, i) => (
                        <li key={i} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                          <span className="text-primary-600 mr-2">•</span>
                          {achievement}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2">
                    {exp.technologies.map((tech, i) => (
                      <span
                        key={i}
                        className="bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-xs font-medium"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default ExperienceSection;
