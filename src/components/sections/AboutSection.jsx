import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  Code2, 
  Database, 
  Server, 
  Smartphone, 
  Cloud, 
  GitBranch,
  Monitor,
  Cpu,
  Globe,
  Shield
} from 'lucide-react';

const AboutSection = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const skills = [
    {
      category: "Programming Languages",
      icon: Code2,
      items: [
        { name: "Java", level: 90 },
        { name: "PHP", level: 85 },
        { name: "Python", level: 88 },
        { name: "JavaScript", level: 92 },
        { name: "TypeScript", level: 85 },
        { name: "Ruby", level: 80 }
      ]
    },
    {
      category: "Web Development",
      icon: Globe,
      items: [
        { name: "React/Next.js", level: 95 },
        { name: "Vue.js", level: 85 },
        { name: "Ruby on Rails", level: 88 },
        { name: "Django", level: 82 },
        { name: "HTML/CSS", level: 95 },
        { name: "RESTful APIs", level: 90 }
      ]
    },
    {
      category: "Database & Backend",
      icon: Database,
      items: [
        { name: "MySQL", level: 90 },
        { name: "PostgreSQL", level: 88 },
        { name: "MongoDB", level: 85 },
        { name: "SQLite", level: 92 },
        { name: "Server Administration", level: 85 }
      ]
    },
    {
      category: "Mobile & Cloud",
      icon: Smartphone,
      items: [
        { name: "React Native", level: 85 },
        { name: "Flutter", level: 80 },
        { name: "AWS", level: 82 },
        { name: "Google Cloud", level: 78 },
        { name: "Docker", level: 85 }
      ]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section id="about" className="py-20 px-4 bg-gray-50 dark:bg-dark-800" ref={ref}>
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent">
            About Me
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-600 to-primary-400 mx-auto mb-8"></div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-start mb-20">
          {/* Bio Section */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            <div className="bg-white dark:bg-dark-700 p-8 rounded-2xl shadow-lg">
              <h3 className="text-2xl font-semibold mb-6 text-gray-900 dark:text-white">
                Professional Journey
              </h3>
              <div className="space-y-4 text-gray-600 dark:text-gray-300 leading-relaxed">
                <p>
                  Brian is an experienced Software Engineer, an ICT consultant and academic with strong expertise in
                  software development, project management and systems integration.
                </p>
                <p>
                  He holds a BSc in Business and Information Technology from the University of Greenwich, UK.
                  Currently serving as a Software Developer at Elizabeth Glaser Pediatric AIDS foundation, Brian
                  and his team have conducted influential research in health and health IT, addressing critical challenges in
                  HIV in Malawi and across Africa.
                </p>
                <p>
                  Brian has experience in various sectors including the financial sector, having worked with the
                  credit reference bureau (Credit Data CRB) as a software engineer, and the education sector with the Ministry
                  of Education through Technix as a software developer.
                </p>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-4">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white dark:bg-dark-700 p-6 rounded-xl shadow-lg text-center"
              >
                <div className="text-3xl font-bold text-primary-600 mb-2">5+</div>
                <div className="text-gray-600 dark:text-gray-300">Years Experience</div>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white dark:bg-dark-700 p-6 rounded-xl shadow-lg text-center"
              >
                <div className="text-3xl font-bold text-primary-600 mb-2">3</div>
                <div className="text-gray-600 dark:text-gray-300">Countries Deployed</div>
              </motion.div>
            </div>
          </motion.div>

          {/* Skills Visualization */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="space-y-6"
          >
            <h3 className="text-2xl font-semibold mb-6 text-gray-900 dark:text-white">
              Technical Expertise
            </h3>
            
            {skills.map((skillCategory, categoryIndex) => (
              <motion.div
                key={skillCategory.category}
                initial={{ opacity: 0, y: 20 }}
                animate={inView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 0.6 + categoryIndex * 0.1 }}
                className="bg-white dark:bg-dark-700 p-6 rounded-xl shadow-lg"
              >
                <div className="flex items-center mb-4">
                  <skillCategory.icon className="text-primary-600 mr-3" size={24} />
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {skillCategory.category}
                  </h4>
                </div>
                
                <div className="space-y-3">
                  {skillCategory.items.map((skill, skillIndex) => (
                    <div key={skill.name} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {skill.name}
                        </span>
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {skill.level}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <motion.div
                          className="bg-gradient-to-r from-primary-600 to-primary-400 h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={inView ? { width: `${skill.level}%` } : { width: 0 }}
                          transition={{ 
                            duration: 1.5, 
                            delay: 0.8 + categoryIndex * 0.1 + skillIndex * 0.1,
                            ease: "easeOut"
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
