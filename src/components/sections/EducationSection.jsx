import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  GraduationCap, 
  Award, 
  BookOpen, 
  Calendar,
  MapPin,
  Star,
  Trophy,
  Target
} from 'lucide-react';

const EducationSection = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const education = [
    {
      id: 1,
      degree: "BSc in Business and Information Technology",
      institution: "University of Greenwich",
      location: "United Kingdom",
      period: "Completed",
      type: "Bachelor's Degree",
      description: "Comprehensive program combining business acumen with technical expertise in information technology, providing a strong foundation for software engineering and project management.",
      highlights: [
        "Business Process Analysis",
        "Software Development Methodologies",
        "Database Design and Management",
        "Project Management",
        "Systems Analysis and Design",
        "IT Strategy and Governance"
      ],
      icon: GraduationCap,
      color: "from-blue-600 to-blue-400"
    },
    {
      id: 2,
      degree: "Advanced Diploma in Computer Science",
      institution: "NCC Education",
      location: "United Kingdom",
      period: "Completed",
      type: "Advanced Diploma",
      description: "Intensive program focusing on core computer science principles, programming fundamentals, and software engineering practices.",
      highlights: [
        "Programming Fundamentals",
        "Data Structures and Algorithms",
        "Software Engineering Principles",
        "Computer Systems Architecture",
        "Database Systems",
        "Web Development Technologies"
      ],
      icon: BookOpen,
      color: "from-green-600 to-green-400"
    }
  ];

  const certifications = [
    {
      id: 1,
      name: "Advanced Google Analytics",
      issuer: "Google",
      type: "Professional Certification",
      description: "Advanced certification in web analytics, data analysis, and digital marketing insights.",
      skills: ["Web Analytics", "Data Analysis", "Digital Marketing", "Reporting"],
      icon: Target,
      color: "from-orange-500 to-red-500"
    }
  ];

  const achievements = [
    {
      id: 1,
      title: "International Project Leadership",
      description: "Successfully led the first international deployment of EMR system to Democratic Republic of Congo",
      icon: Trophy,
      color: "text-yellow-500"
    },
    {
      id: 2,
      title: "Multi-Sector Expertise",
      description: "Demonstrated versatility across healthcare, education, and financial technology sectors",
      icon: Star,
      color: "text-blue-500"
    },
    {
      id: 3,
      title: "Technical Innovation",
      description: "Developed scalable solutions serving thousands of users across multiple countries",
      icon: Award,
      color: "text-green-500"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="education" className="py-20 px-4 bg-white dark:bg-dark-900" ref={ref}>
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent">
            Education & Qualifications
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-600 to-primary-400 mx-auto mb-8"></div>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Academic foundation and professional certifications that drive technical excellence
          </p>
        </motion.div>

        {/* Education */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="mb-16"
        >
          <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-8 text-center">
            Academic Qualifications
          </h3>
          
          <div className="space-y-8">
            {education.map((edu, index) => (
              <motion.div
                key={edu.id}
                variants={itemVariants}
                className="relative"
              >
                <motion.div
                  whileHover={{ scale: 1.02, y: -5 }}
                  className="bg-gray-50 dark:bg-dark-800 p-8 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-600"
                >
                  <div className="flex flex-col md:flex-row md:items-start md:space-x-6">
                    {/* Icon */}
                    <div className={`flex-shrink-0 p-4 rounded-xl bg-gradient-to-r ${edu.color} mb-4 md:mb-0`}>
                      <edu.icon className="text-white" size={32} />
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-4">
                        <div>
                          <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                            {edu.degree}
                          </h4>
                          <p className="text-primary-600 dark:text-primary-400 font-medium text-lg">
                            {edu.institution}
                          </p>
                        </div>
                        <div className="mt-2 md:mt-0 text-right">
                          <span className="bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 px-3 py-1 rounded-full text-sm font-medium">
                            {edu.type}
                          </span>
                          <div className="flex items-center justify-end mt-2 text-sm text-gray-600 dark:text-gray-400">
                            <MapPin size={16} className="mr-1" />
                            {edu.location}
                          </div>
                        </div>
                      </div>

                      <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                        {edu.description}
                      </p>

                      {/* Highlights */}
                      <div>
                        <h5 className="font-semibold text-gray-900 dark:text-white mb-3">Key Areas of Study:</h5>
                        <div className="grid md:grid-cols-2 gap-2">
                          {edu.highlights.map((highlight, i) => (
                            <div key={i} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <span className="text-primary-600 mr-2">•</span>
                              {highlight}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Certifications */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="mb-16"
        >
          <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-8 text-center">
            Professional Certifications
          </h3>
          
          <div className="grid md:grid-cols-1 gap-6">
            {certifications.map((cert) => (
              <motion.div
                key={cert.id}
                variants={itemVariants}
                whileHover={{ scale: 1.02 }}
                className="bg-gray-50 dark:bg-dark-800 p-6 rounded-xl shadow-lg border border-gray-100 dark:border-gray-600"
              >
                <div className="flex items-start space-x-4">
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${cert.color}`}>
                    <cert.icon className="text-white" size={24} />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                      {cert.name}
                    </h4>
                    <p className="text-primary-600 dark:text-primary-400 font-medium mb-2">
                      {cert.issuer} • {cert.type}
                    </p>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      {cert.description}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {cert.skills.map((skill, i) => (
                        <span
                          key={i}
                          className="bg-white dark:bg-dark-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-xs font-medium border border-gray-200 dark:border-gray-600"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Achievements */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-8 text-center">
            Key Achievements
          </h3>
          
          <div className="grid md:grid-cols-3 gap-6">
            {achievements.map((achievement) => (
              <motion.div
                key={achievement.id}
                variants={itemVariants}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-gray-50 dark:bg-dark-800 p-6 rounded-xl shadow-lg border border-gray-100 dark:border-gray-600 text-center"
              >
                <achievement.icon className={`mx-auto mb-4 ${achievement.color}`} size={48} />
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {achievement.title}
                </h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                  {achievement.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default EducationSection;
