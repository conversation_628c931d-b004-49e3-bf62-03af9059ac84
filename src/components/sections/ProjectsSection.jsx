import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  ExternalLink, 
  Github, 
  Heart, 
  GraduationCap, 
  CreditCard,
  Globe,
  Users,
  Database,
  Shield,
  Zap,
  Award,
  Target
} from 'lucide-react';

const ProjectsSection = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const [selectedCategory, setSelectedCategory] = useState('all');

  const projects = [
    {
      id: 1,
      title: "Health Information Systems (HIS)",
      category: "healthcare",
      company: "Elizabeth Glazer Pediatric Foundation",
      period: "Jan 2023 - Present",
      description: "Electronic medical record system (EMR) used across Malawi for HIV treatment and care. Successfully led the first international deployment to Democratic Republic of Congo.",
      longDescription: "A comprehensive healthcare management system that revolutionizes patient care across multiple countries. The system handles patient registration, treatment protocols, medication management, and reporting for HIV/AIDS care programs.",
      image: "/api/placeholder/600/400",
      technologies: ["Java", "PHP", "MySQL", "JavaScript", "Healthcare APIs", "International Deployment"],
      features: [
        "Multi-country deployment capability",
        "Patient treatment tracking",
        "Medication management",
        "Comprehensive reporting system",
        "Real-time data synchronization",
        "Multilingual support"
      ],
      impact: {
        patients: "10,000+",
        countries: "2",
        facilities: "50+"
      },
      icon: Heart,
      color: "from-red-500 to-pink-500",
      status: "Active"
    },
    {
      id: 2,
      title: "School Inspection System",
      category: "education",
      company: "Technix / Ministry of Education",
      period: "Mar 2022 - Dec 2022",
      description: "Comprehensive school inspection platform used by the Ministry of Education to monitor and improve education quality across Malawi.",
      longDescription: "A digital transformation initiative that modernized the school inspection process, enabling efficient monitoring of educational standards and facilitating data-driven decision making for education policy.",
      image: "/api/placeholder/600/400",
      technologies: ["PHP", "JavaScript", "MySQL", "Bootstrap", "Government Integration", "Reporting"],
      features: [
        "Digital inspection forms",
        "Real-time data collection",
        "Automated report generation",
        "Performance analytics",
        "Multi-level user access",
        "Mobile-responsive design"
      ],
      impact: {
        schools: "500+",
        inspectors: "100+",
        reports: "1,000+"
      },
      icon: GraduationCap,
      color: "from-blue-500 to-cyan-500",
      status: "Completed"
    },
    {
      id: 3,
      title: "Credit Reporting System",
      category: "fintech",
      company: "Credit Data CRB",
      period: "Jul 2018 - Feb 2020",
      description: "In-house credit reporting system that generates comprehensive credit reports for banks and financial institutions across Malawi.",
      longDescription: "A secure financial data processing system that aggregates credit information from multiple sources to provide accurate credit assessments, helping financial institutions make informed lending decisions.",
      image: "/api/placeholder/600/400",
      technologies: ["Java", "Spring Boot", "PostgreSQL", "REST APIs", "Financial Security", "Data Analytics"],
      features: [
        "Multi-source data aggregation",
        "Real-time credit scoring",
        "Secure data processing",
        "Regulatory compliance",
        "API integration",
        "Automated report generation"
      ],
      impact: {
        banks: "15+",
        reports: "50,000+",
        accuracy: "99.5%"
      },
      icon: CreditCard,
      color: "from-green-500 to-emerald-500",
      status: "Completed"
    },
    {
      id: 4,
      title: "International Consulting Projects",
      category: "consulting",
      company: "Aurora Borealis",
      period: "Sep 2021 - Mar 2022",
      description: "Remote software development consultancy for international clients, delivering scalable solutions across various industries.",
      longDescription: "Provided expert software development consultancy services for international projects, focusing on scalable architecture design and implementation of best practices for global software solutions.",
      image: "/api/placeholder/600/400",
      technologies: ["Remote Collaboration", "Full-stack Development", "Cloud Architecture", "International Standards"],
      features: [
        "Cross-timezone collaboration",
        "Scalable architecture design",
        "Code quality assurance",
        "International best practices",
        "Remote team leadership",
        "Client relationship management"
      ],
      impact: {
        clients: "5+",
        countries: "3",
        satisfaction: "100%"
      },
      icon: Globe,
      color: "from-purple-500 to-violet-500",
      status: "Completed"
    }
  ];

  const categories = [
    { id: 'all', name: 'All Projects', count: projects.length },
    { id: 'healthcare', name: 'Healthcare', count: projects.filter(p => p.category === 'healthcare').length },
    { id: 'education', name: 'Education', count: projects.filter(p => p.category === 'education').length },
    { id: 'fintech', name: 'FinTech', count: projects.filter(p => p.category === 'fintech').length },
    { id: 'consulting', name: 'Consulting', count: projects.filter(p => p.category === 'consulting').length }
  ];

  const filteredProjects = selectedCategory === 'all' 
    ? projects 
    : projects.filter(project => project.category === selectedCategory);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="projects" className="py-20 px-4 bg-gray-50 dark:bg-dark-800" ref={ref}>
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent">
            Featured Projects
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-600 to-primary-400 mx-auto mb-8"></div>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Impactful software solutions across healthcare, education, and financial sectors
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <motion.button
              key={category.id}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'bg-primary-600 text-white shadow-lg'
                  : 'bg-white dark:bg-dark-700 text-gray-700 dark:text-gray-300 hover:bg-primary-50 dark:hover:bg-dark-600'
              }`}
            >
              {category.name} ({category.count})
            </motion.button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedCategory}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            className="grid md:grid-cols-2 gap-8"
          >
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                variants={itemVariants}
                layout
                className="group"
              >
                <motion.div
                  whileHover={{ y: -10, scale: 1.02 }}
                  className="bg-white dark:bg-dark-700 rounded-2xl shadow-lg overflow-hidden border border-gray-100 dark:border-gray-600 h-full"
                >
                  {/* Project Header */}
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`p-3 rounded-lg bg-gradient-to-r ${project.color}`}>
                          <project.icon className="text-white" size={24} />
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            {project.title}
                          </h3>
                          <p className="text-primary-600 dark:text-primary-400 font-medium">
                            {project.company}
                          </p>
                        </div>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        project.status === 'Active' 
                          ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                          : 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                      }`}>
                        {project.status}
                      </span>
                    </div>

                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">{project.period}</p>
                    <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                      {project.description}
                    </p>

                    {/* Impact Metrics */}
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      {Object.entries(project.impact).map(([key, value]) => (
                        <div key={key} className="text-center">
                          <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                            {value}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                            {key}
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Technologies */}
                    <div className="flex flex-wrap gap-2 mb-6">
                      {project.technologies.slice(0, 4).map((tech, i) => (
                        <span
                          key={i}
                          className="bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-xs font-medium"
                        >
                          {tech}
                        </span>
                      ))}
                      {project.technologies.length > 4 && (
                        <span className="bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 px-3 py-1 rounded-full text-xs font-medium">
                          +{project.technologies.length - 4} more
                        </span>
                      )}
                    </div>

                    {/* Key Features */}
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Key Features:</h4>
                      <ul className="space-y-1">
                        {project.features.slice(0, 3).map((feature, i) => (
                          <li key={i} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                            <span className="text-primary-600 mr-2">•</span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>
      </div>
    </section>
  );
};

export default ProjectsSection;
