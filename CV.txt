BRIAN MSYAMBOZA | SOFTWARE ENGINEER
Phone: +265881398343 / +265994314317
Email: brianmsyam<PERSON><EMAIL>
Address: C/O EGPAF, 4/5th Floor, Gemini House City Center, Lilongwe
<PERSON><PERSON> is an experienced Software Engineer, an ICT consultant and academic with strong expertise in
software development, project management and systems integration.
He holds a BSc in Business and Information Technology from the university of Greenwich, UK.
Currently serving as an Software Developer at Elizabeth Glzer Pediatric AIDS foundation, <PERSON>
and team has conducted influential research in health and health IT, addressing critical challenges in
HIV in Malawi and across Africa.
<PERSON> has also experience in various sectors including the financial sector having worked with the
credit reference bureau (Credit data CRB) as a software engineer, Education sector with the ministry
of education through Technix as a software developer.
<PERSON> has been consulting with different sectors. He is Fluent in English, Chichewa, he is dedicated
to driving technological advancements and enhancing digital
QUALIFICATIONS
•BSc in Business and Information Technology, University of Greenwich, UK
•Ad. diploma in Computer Science, NCC, UK
•Advanced Google Analytics
EMPLOYMENT HISTORY
•Jan 2023 to Present: Software Developer, Elizabeth Glazer Pediatric Foundation (EGPAF)
•Mar 2022 to Dec 2022: Software Developer, Technix
•Sept 2021 to March 2022: Software Consultancy, Auroal Borealis, South Africa (Remote)
•Feb 2020 to Dec 2021: Software Engineer Lead, Baobab Health Trust
•July 2018 to Feb 2020: Software Engineer & Database Officer, Credit Data CRBRELEVANT PROJECT EXPERIENCE
•Software Engineer, Health Information Systems (HIS) Project | Elizabeth Glazer Pediatric
Foundation (Jan 2023 - Present) Develop the Electronic medical record system (EMR) used across
the country for treatment and care, Led the first international deployment of the EMR in
Democratic republic of Congo
•Software Developer, School Inspection System | Technix (Mar 2022 - Dec 2022) Developed
and maintained the school inspection system used by the ministry of education.
•Software Engineer, Internal Systems | Credit Data CRB, (Jun 2018 – Feb 2020) Developed
in house systems to produce credit reports for banks in Malawi
SKILLS
Software Programming (Java, PHP, Python, JavaScript, Typescript, Ruby, HTML, CSS, SQL, etc) Web
Development Frameworks (Ruby on Rails, NextJs, Vue, Django, etc) Software Testing and Debugging
Networking Configuration and Support Windows and Linux Server Administration Database
Management and Administration (MySQL, PostgreSQL, MongoDB, SQLite) Mobile App Development
(React Native, Capacitor, Flutter) Version Control Systems (git, bitbucket) Cloud Services (AWS,
Google cloud) RESTful APIs
REFERENCES
Mr. Roy Chanunkha
EGPAF | Senior Software Developer
Email : <EMAIL>
Mr. Pachawo Bisani
Baobab |Software Engineer Lead
Email : <EMAIL>
Mr. Blessings Mwafulirwa
Credit Data CRB | Systems Administrator
Email : <EMAIL>